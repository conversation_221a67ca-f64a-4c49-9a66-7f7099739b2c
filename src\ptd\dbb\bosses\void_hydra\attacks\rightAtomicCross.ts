import { Entity, EntityDamageCause, Vector3, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { getDistance } from "../../../utilities/vector3";

/**
 * Attack timing constants for right atomic cross attack
 */
const DAMAGE_TIMING = 60; // Apply damage at tick 60
const ANIMATION_TIME = 120; // Total animation time in ticks
const COOLDOWN_TIME = 20; // Cooldown time in ticks after the attack completes

/**
 * Attack configuration
 */
const ATTACK_CONFIG = {
  /** Range of the atomic cross attack */
  RANGE: 16,
  /** Width of the cross beams */
  BEAM_WIDTH: 3,
  /** Particle density for visual effects */
  PARTICLE_DENSITY: 8
};

/**
 * Executes the right atomic cross attack for the Void Hydra
 * Creates a cross-shaped energy blast that damages entities in its path
 *
 * @param voidHydra The void hydra entity
 */
export function executeRightAtomicCrossAttack(voidHydra: Entity): void {
  // Apply damage at tick 60
  let damageTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(damageTiming);
        return;
      }

      if (voidHydra.getProperty("ptd_dbb:attack") === "right_atomic_cross") {
        performAtomicCrossDamage(voidHydra);
      }
    } catch (error) {
      system.clearRun(damageTiming);
    }
  }, DAMAGE_TIMING);

  // Reset attack and start cooldown after animation completes
  let resetTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetTiming);
        return;
      }

      if (voidHydra.getProperty("ptd_dbb:attack") === "right_atomic_cross") {
        voidHydra.triggerEvent("ptd_dbb:reset_attack");
      }
    } catch (error) {
      system.clearRun(resetTiming);
    }
  }, ANIMATION_TIME);

  // End cooldown after cooldown time
  let cooldownTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTiming);
        return;
      }

      voidHydra.setProperty("ptd_dbb:cooling_down", false);
    } catch (error) {
      system.clearRun(cooldownTiming);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
}

/**
 * Performs the atomic cross damage in a cross pattern
 * @param voidHydra The void hydra entity
 */
function performAtomicCrossDamage(voidHydra: Entity): void {
  try {
    const origin: Vector3 = voidHydra.location;
    const damage = VOID_HYDRA_ATTACK_DAMAGES.right_atomic_cross.damage;

    // Create cross pattern: horizontal and vertical beams
    const crossDirections = [
      { x: 1, z: 0 },   // East
      { x: -1, z: 0 },  // West
      { x: 0, z: 1 },   // South
      { x: 0, z: -1 }   // North
    ];

    // For each direction of the cross
    crossDirections.forEach(direction => {
      // Create beam in this direction
      for (let distance = 1; distance <= ATTACK_CONFIG.RANGE; distance++) {
        // Calculate beam positions with width
        for (let width = -ATTACK_CONFIG.BEAM_WIDTH; width <= ATTACK_CONFIG.BEAM_WIDTH; width++) {
          let beamPos: Vector3;
          
          if (direction.x !== 0) {
            // Horizontal beam (vary Z position for width)
            beamPos = {
              x: origin.x + direction.x * distance,
              y: origin.y,
              z: origin.z + width
            };
          } else {
            // Vertical beam (vary X position for width)
            beamPos = {
              x: origin.x + width,
              y: origin.y,
              z: origin.z + direction.z * distance
            };
          }

          // Damage entities at this position
          voidHydra.dimension
            .getEntities({
              location: beamPos,
              maxDistance: 1.5,
              excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
              excludeFamilies: ["void_hydra", "boss"]
            })
            .forEach((entity) => {
              entity.applyDamage(damage, { 
                cause: EntityDamageCause.entityAttack, 
                damagingEntity: voidHydra 
              });
            });

          // Spawn particles for visual effect
          if (distance % 2 === 0) { // Reduce particle density
            voidHydra.dimension.spawnParticle("minecraft:dragon_breath_trail", beamPos);
          }
        }
      }
    });

    // Spawn explosion particle at origin
    voidHydra.dimension.spawnParticle("minecraft:large_explosion", origin);
  } catch (error) {
    // Handle errors silently
  }
}
