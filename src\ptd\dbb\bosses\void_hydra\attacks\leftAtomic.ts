import { Enti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e, Vector3, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { getTarget } from "../../general_mechanics/targetUtils";

const DAMAGE_TIMING = 50;
const ANIMATION_TIME = 100;
const COOLDOWN_TIME = 20;

const ATTACK_CONFIG = {
  EXPLOSION_RADIUS: 4,
};

export function executeLeftAtomicAttack(voidHydra: Entity): void {
  let damageTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(damageTiming);
        return;
      }

      if (voidHydra.getProperty("ptd_dbb:attack") === "left_atomic") {
        performAtomicBlast(voidHydra);
      }
    } catch (error) {
      system.clearRun(damageTiming);
    }
  }, DAMAGE_TIMING);

  let resetTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(resetTiming);
        return;
      }

      if (voidHydra.getProperty("ptd_dbb:attack") === "left_atomic") {
        voidHydra.triggerEvent("ptd_dbb:reset_attack");
      }
    } catch (error) {
      system.clearRun(resetTiming);
    }
  }, ANIMATION_TIME);

  let cooldownTiming = system.runTimeout(() => {
    try {
      const isDead = voidHydra.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTiming);
        return;
      }

      voidHydra.setProperty("ptd_dbb:cooling_down", false);
    } catch (error) {
      system.clearRun(cooldownTiming);
    }
  }, ANIMATION_TIME + COOLDOWN_TIME);
}

function performAtomicBlast(voidHydra: Entity): void {
  try {
    const target = getTarget(voidHydra);
    if (!target) return;

    const targetPos: Vector3 = target.location;
    const damage = VOID_HYDRA_ATTACK_DAMAGES.left_atomic.damage;

    voidHydra.dimension
      .getEntities({
        location: targetPos,
        maxDistance: ATTACK_CONFIG.EXPLOSION_RADIUS,
        excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
        excludeFamilies: ["void_hydra", "boss"]
      })
      .forEach((entity) => {
        entity.applyDamage(damage, { 
          cause: EntityDamageCause.entityAttack, 
          damagingEntity: voidHydra 
        });

        try {
          const knockbackDirection = {
            x: entity.location.x - targetPos.x,
            y: 0.3,
            z: entity.location.z - targetPos.z
          };
          
          const magnitude = Math.sqrt(knockbackDirection.x ** 2 + knockbackDirection.z ** 2);
          if (magnitude > 0) {
            knockbackDirection.x = (knockbackDirection.x / magnitude) * 0.8;
            knockbackDirection.z = (knockbackDirection.z / magnitude) * 0.8;
          }

          entity.applyKnockback(knockbackDirection.x, knockbackDirection.z, 0.8, 0.3);
        } catch (knockbackError) {
          try {
            const impulse = {
              x: (entity.location.x - targetPos.x) * 0.3,
              y: 0.2,
              z: (entity.location.z - targetPos.z) * 0.3
            };
            entity.applyImpulse(impulse);
          } catch (impulseError) {
            // Ignore if both methods fail
          }
        }
      });

    voidHydra.dimension.spawnParticle("minecraft:huge_explosion_emitter", targetPos);
  } catch (error) {
    // Handle errors silently
  }
}
