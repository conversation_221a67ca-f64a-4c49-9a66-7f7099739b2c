import { Entity } from "@minecraft/server";
import { handleAttackLogic, selectAttack } from "./controller";
import { stopVoidHydraSounds } from "./soundManager";
import { spawnItemFountain } from "../general_mechanics/itemFountain";
import { getTarget } from "../general_mechanics/targetUtils";
import { applyDeathCameraShake, handleDeathMechanics } from "../general_mechanics/deathMechanics";
import { cameraShake } from "../general_effects/camerashake";

// Import all attack functions
import { executeRightAtomicCrossAttack } from "./attacks/rightAtomicCross";
import { executeRightAtomicAttack } from "./attacks/rightAtomic";
import { executeRightVacuumAttack } from "./attacks/rightVacuum";
import { executeRightSummonAttack } from "./attacks/rightSummon";
import { executeMidAtomicAttack } from "./attacks/midAtomic";
import { executeMidMeteorAttack } from "./attacks/midMeteor";
import { executeMidSingularityAttack } from "./attacks/midSingularity";
import { executeLeftAtomicCrossAttack } from "./attacks/leftAtomicCross";
import { executeLeftAtomicAttack } from "./attacks/leftAtomic";
import { executeLeftRailgunAttack } from "./attacks/leftRailgun";
import { executeLeftMissileAttack } from "./attacks/leftMissile";
import { executeLeftShoutAttack } from "./attacks/leftShout";

/**
 * Handles the void hydra boss mechanics
 * Currently implements spawning, idle, and movement mechanics
 *
 * @param voidHydra The void hydra entity
 */
export function voidHydraMechanics(voidHydra: Entity): void {
  try {
    // Skip if entity is not valid
    if (!voidHydra) return;

    // Handle death mechanics using the generalized function
    // If the entity is dead, this will handle all death-related behavior and return true
    if (
      handleDeathMechanics(voidHydra, {
        // Configure death mechanics specific to the Void Hydra
        duration: 150,
        xpOrbs: {
          count: 8,
          duration: 100,
          heightOffset: 2.25
        },
        // No drops here as we'll use a custom event to spawn the essence fountain
        drops: [],
        deathSound: "mob.ptd_dbb_void_hydra.death",
        // Add custom event to spawn essence fountain at the beginning of death sequence
        customEvents: [
          {
            tick: 1,
            callback: (entity) => {
              applyDeathCameraShake(voidHydra, 150);
              // Spawn 32 essence items in a fountain-like effect
              spawnItemFountain(entity, "ptd_dbb:void_hydra_essence", 32, {
                heightOffset: 2.25,
                particleEffect: "minecraft:large_explosion",
                soundEffect: "random.pop",
                minVerticalStrength: 0.1,
                maxVerticalStrength: 0.3,
                minHorizontalStrength: 0.05,
                maxHorizontalStrength: 0.2
              });
            }
          }
        ],
        // Provide the sound stopping function
        stopSoundsFn: (entity: Entity, excludedSound?: string) => stopVoidHydraSounds(entity, excludedSound)
      })
    ) {
      // If death mechanics were applied, return early
      return;
    }

    // Check if the entity is spawning
    const isSpawning = voidHydra.getProperty("ptd_dbb:spawning") as boolean;

    // Handle spawning mechanics
    if (isSpawning) {
      // Get current spawning ticks
      const currentTicks = voidHydra.getProperty("ptd_dbb:spawning_ticks") as number;

      // Increment spawning ticks, capped at 120 (total animation length)
      const newTicks = Math.min(currentTicks + 1, 280);
      voidHydra.setProperty("ptd_dbb:spawning_ticks", newTicks);

      // Apply effects on specific ticks during spawning
      if (currentTicks === 5 && newTicks === 6) {
        // Apply camera shake effect
        cameraShake(voidHydra, 32, 0.02, 0.5, 0.5);

        // Play a particle effect at the void hydra's location
        voidHydra.dimension.spawnParticle("minecraft:large_explosion", voidHydra.location);
      }

      // When spawning is complete, set the spawning property to false
      if (newTicks >= 120) {
        voidHydra.setProperty("ptd_dbb:spawning", false);

        // Face the nearest player
        const nearestPlayer = getTarget(voidHydra, voidHydra.location, 32, ["void_hydra"]);
        if (nearestPlayer) {
          // Calculate direction to face
          const dx = nearestPlayer.location.x - voidHydra.location.x;
          const dz = nearestPlayer.location.z - voidHydra.location.z;
          const yaw = Math.atan2(-dx, dz) * (180 / Math.PI);

          // Set the rotation
          voidHydra.setRotation({ x: 0, y: yaw });
        }
      }

      return;
    }

    // Handle attack cooldown
    const attackCooldown = voidHydra.getProperty("ptd_dbb:attack_cooldown") as number;
    if (attackCooldown > 0) {
      voidHydra.setProperty("ptd_dbb:attack_cooldown", attackCooldown - 1);
    }

    // Get current attack and timer
    const attack = voidHydra.getProperty("ptd_dbb:attack") as string;
    const attackTimer = voidHydra.getProperty("ptd_dbb:attack_timer") as number;

    // If not attacking, try to select an attack
    if (attack === "none") {
      // Find a target for the attack
      const target = getTarget(voidHydra, voidHydra.location, 32, ["void_hydra"]);
      if (target) {
        selectAttack(voidHydra, target);
      }
    } else {
      // Execute attack-specific logic
      executeAttackLogic(voidHydra, attack, attackTimer);

      // Handle the current attack
      handleAttackLogic(voidHydra, attack, attackTimer);
    }
  } catch (error) {
    console.warn(`Error in void hydra mechanics: ${error}`);
  }
}

/**
 * Executes attack-specific logic based on the current attack and timer
 * @param voidHydra The void hydra entity
 * @param attack The current attack type
 * @param attackTimer The current attack timer
 */
function executeAttackLogic(voidHydra: Entity, attack: string, attackTimer: number): void {
  // Only execute attack logic once when the attack starts (timer = 1)
  if (attackTimer !== 1) return;

  try {
    switch (attack) {
      case "right_atomic_cross":
        executeRightAtomicCrossAttack(voidHydra);
        break;
      case "right_atomic":
        executeRightAtomicAttack(voidHydra);
        break;
      case "right_vacuum":
        executeRightVacuumAttack(voidHydra);
        break;
      case "right_summon":
        executeRightSummonAttack(voidHydra);
        break;
      case "mid_atomic":
        executeMidAtomicAttack(voidHydra);
        break;
      case "mid_meteor":
        executeMidMeteorAttack(voidHydra);
        break;
      case "mid_singularity":
        executeMidSingularityAttack(voidHydra);
        break;
      case "left_atomic_cross":
        executeLeftAtomicCrossAttack(voidHydra);
        break;
      case "left_atomic":
        executeLeftAtomicAttack(voidHydra);
        break;
      case "left_railgun":
        executeLeftRailgunAttack(voidHydra);
        break;
      case "left_missile":
        executeLeftMissileAttack(voidHydra);
        break;
      case "left_shout":
        executeLeftShoutAttack(voidHydra);
        break;
      default:
        // Unknown attack type
        break;
    }
  } catch (error) {
    console.warn(`Error executing attack ${attack}: ${error}`);
  }
}
