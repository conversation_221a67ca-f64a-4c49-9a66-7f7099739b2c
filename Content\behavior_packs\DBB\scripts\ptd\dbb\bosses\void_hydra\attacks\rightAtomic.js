import { EntityDamageCause, system } from "@minecraft/server";
import { VOID_HYDRA_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
import { getTarget } from "../../general_mechanics/targetUtils";
/**
 * Attack timing constants for right atomic attack
 */
const DAMAGE_TIMING = 50; // Apply damage at tick 50
const ANIMATION_TIME = 100; // Total animation time in ticks
const COOLDOWN_TIME = 20; // Cooldown time in ticks after the attack completes
/**
 * Attack configuration
 */
const ATTACK_CONFIG = {
    /** Range of the atomic blast */
    RANGE: 12,
    /** Radius of the explosion */
    EXPLOSION_RADIUS: 4,
    /** Number of particles to spawn */
    PARTICLE_COUNT: 20
};
/**
 * Executes the right atomic attack for the Void Hydra
 * Creates a targeted atomic blast at the target's location
 *
 * @param voidHydra The void hydra entity
 */
export function executeRightAtomicAttack(voidHydra) {
    // Apply damage at tick 50
    let damageTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(damageTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "right_atomic") {
                performAtomicBlast(voidHydra);
            }
        }
        catch (error) {
            system.clearRun(damageTiming);
        }
    }, DAMAGE_TIMING);
    // Reset attack and start cooldown after animation completes
    let resetTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(resetTiming);
                return;
            }
            if (voidHydra.getProperty("ptd_dbb:attack") === "right_atomic") {
                voidHydra.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            system.clearRun(resetTiming);
        }
    }, ANIMATION_TIME);
    // End cooldown after cooldown time
    let cooldownTiming = system.runTimeout(() => {
        try {
            const isDead = voidHydra.getProperty("ptd_dbb:dead");
            if (isDead) {
                system.clearRun(cooldownTiming);
                return;
            }
            voidHydra.setProperty("ptd_dbb:cooling_down", false);
        }
        catch (error) {
            system.clearRun(cooldownTiming);
        }
    }, ANIMATION_TIME + COOLDOWN_TIME);
}
/**
 * Performs the atomic blast damage at the target's location
 * @param voidHydra The void hydra entity
 */
function performAtomicBlast(voidHydra) {
    try {
        const target = getTarget(voidHydra);
        if (!target)
            return;
        const targetPos = target.location;
        const damage = VOID_HYDRA_ATTACK_DAMAGES.right_atomic.damage;
        // Apply damage to entities in the explosion radius
        voidHydra.dimension
            .getEntities({
            location: targetPos,
            maxDistance: ATTACK_CONFIG.EXPLOSION_RADIUS,
            excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
            excludeFamilies: ["void_hydra", "boss"]
        })
            .forEach((entity) => {
            entity.applyDamage(damage, {
                cause: EntityDamageCause.entityAttack,
                damagingEntity: voidHydra
            });
            // Apply knockback effect
            try {
                const knockbackDirection = {
                    x: entity.location.x - targetPos.x,
                    y: 0.3, // Slight upward knockback
                    z: entity.location.z - targetPos.z
                };
                // Normalize and apply knockback
                const magnitude = Math.sqrt(knockbackDirection.x ** 2 + knockbackDirection.z ** 2);
                if (magnitude > 0) {
                    knockbackDirection.x = (knockbackDirection.x / magnitude) * 0.8;
                    knockbackDirection.z = (knockbackDirection.z / magnitude) * 0.8;
                }
                entity.applyKnockback(knockbackDirection.x, knockbackDirection.z, 0.8, 0.3);
            }
            catch (knockbackError) {
                // Fallback to applyImpulse if applyKnockback fails
                try {
                    const impulse = {
                        x: (entity.location.x - targetPos.x) * 0.3,
                        y: 0.2,
                        z: (entity.location.z - targetPos.z) * 0.3
                    };
                    entity.applyImpulse(impulse);
                }
                catch (impulseError) {
                    // Ignore if both methods fail
                }
            }
        });
        // Spawn explosion effects
        voidHydra.dimension.spawnParticle("minecraft:huge_explosion_emitter", targetPos);
        // Spawn additional particles in a circle around the blast
        for (let i = 0; i < ATTACK_CONFIG.PARTICLE_COUNT; i++) {
            const angle = (i / ATTACK_CONFIG.PARTICLE_COUNT) * Math.PI * 2;
            const radius = ATTACK_CONFIG.EXPLOSION_RADIUS * 0.8;
            const particlePos = {
                x: targetPos.x + Math.cos(angle) * radius,
                y: targetPos.y + Math.random() * 2,
                z: targetPos.z + Math.sin(angle) * radius
            };
            voidHydra.dimension.spawnParticle("minecraft:dragon_breath_trail", particlePos);
        }
    }
    catch (error) {
        // Handle errors silently
    }
}
