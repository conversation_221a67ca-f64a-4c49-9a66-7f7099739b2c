{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb.void_hydra.attack": {"states": {"default": {"transitions": [{"right_atomic_cross": "q.property('ptd_dbb:attack') == 'right_atomic_cross'"}, {"right_atomic": "q.property('ptd_dbb:attack') == 'right_atomic'"}, {"right_vacuum": "q.property('ptd_dbb:attack') == 'right_vacuum'"}, {"right_summon": "q.property('ptd_dbb:attack') == 'right_summon'"}, {"mid_atomic": "q.property('ptd_dbb:attack') == 'mid_atomic'"}, {"mid_meteor": "q.property('ptd_dbb:attack') == 'mid_meteor'"}, {"mid_singularity": "q.property('ptd_dbb:attack') == 'mid_singularity'"}, {"left_atomic_cross": "q.property('ptd_dbb:attack') == 'left_atomic_cross'"}, {"left_atomic": "q.property('ptd_dbb:attack') == 'left_atomic'"}, {"left_railgun": "q.property('ptd_dbb:attack') == 'left_railgun'"}, {"left_missile": "q.property('ptd_dbb:attack') == 'left_missile'"}, {"left_shout": "q.property('ptd_dbb:attack') == 'left_shout'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "right_atomic_cross": {"animations": ["right_atomic_cross"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "right_atomic": {"animations": ["right_atomic"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "right_vacuum": {"animations": ["right_vacuum"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "right_summon": {"animations": ["right_summon"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "mid_atomic": {"animations": ["mid_atomic"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "mid_meteor": {"animations": ["mid_meteor"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "mid_singularity": {"animations": ["mid_singularity"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "left_atomic_cross": {"animations": ["left_atomic_cross"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "left_atomic": {"animations": ["left_atomic"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "left_railgun": {"animations": ["left_railgun"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "left_missile": {"animations": ["left_missile"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "left_shout": {"animations": ["left_shout"], "transitions": [{"default": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}]}, "dead": {}}}}}