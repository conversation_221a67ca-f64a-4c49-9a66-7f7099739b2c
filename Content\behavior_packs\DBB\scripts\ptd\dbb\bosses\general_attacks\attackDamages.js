/**
 * Piglin Champion attack damages as direct values
 */
export const PIGLIN_CHAMPION_ATTACK_DAMAGES = {
    horizontal: { damage: 16 },
    vertical: {
        axe: { damage: 16 },
        rocks: { damage: 8 }
    },
    foot_stomp: { damage: 12 },
    spin_slam: { damage: 12 },
    body_slam: { damage: 18 },
    charging: { damage: 20 }
};
/**
 * Necromancer attack damages as direct values
 */
export const NECROMANCER_ATTACK_DAMAGES = {
    cataclysm: { damage: 6 },
    soul_drain: { damage: 7 }
};
/**
 * Grimhowl attack damages as direct values
 */
export const GRIMHOWL_ATTACK_DAMAGES = {
    left_claw: { damage: 4 },
    right_claw: { damage: 4 },
    backstep_sword: { damage: 5 },
    pounce: { damage: 8 },
    shadow_onslaught: { damage: 8 },
    slash: { damage: 8 },
    spinning_slash: { damage: 6 },
    roar: { damage: 2 },
    collision_damage: { damage: 4 }
};
